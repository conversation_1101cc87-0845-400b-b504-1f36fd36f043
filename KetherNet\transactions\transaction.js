const { keccak256 } = require('ethereum-cryptography/keccak');
const { toHex, utf8ToBytes } = require('ethereum-cryptography/utils');
const crypto = require('crypto');

/**
 * KetherNet Transaction with Consciousness Validation
 * Extends standard blockchain transactions with consciousness-aware features
 */
class Transaction {
  /**
   * Create a new consciousness-aware transaction
   * @param {Object} tx - Transaction data
   * @param {string} tx.from - Sender address
   * @param {string} tx.to - Recipient address
   * @param {string|BN} tx.value - Amount to transfer (in wei)
   * @param {string|BN} tx.nonce - Sender's transaction count
   * @param {string|<PERSON>uffer} tx.data - Transaction data
   * @param {string|BN} tx.gasPrice - Gas price in wei
   * @param {string|BN} tx.gasLimit - Gas limit
   * @param {number} tx.consciousnessLevel - Sender's consciousness level (UUFT score)
   * @param {string} tx.consciousnessProof - Proof of consciousness validation
   */
  constructor({ 
    from, 
    to, 
    value, 
    nonce, 
    data = '0x', 
    gasPrice = '0', 
    gasLimit = '21000',
    consciousnessLevel = 2847,
    consciousnessProof = null
  }) {
    this.from = from;
    this.to = to;
    this.value = typeof value === 'string' ? value : value.toString();
    this.nonce = typeof nonce === 'string' ? nonce : nonce.toString();
    this.data = data;
    this.gasPrice = typeof gasPrice === 'string' ? gasPrice : gasPrice.toString();
    this.gasLimit = typeof gasLimit === 'string' ? gasLimit : gasLimit.toString();
    this.consciousnessLevel = consciousnessLevel;
    this.consciousnessProof = consciousnessProof;
    this.signature = null;
    this.timestamp = Date.now();
  }

  /**
   * Sign the transaction with a private key
   * @param {string} privateKey - Sender's private key
   * @returns {string} - Signature
   */
  sign(privateKey) {
    const txHash = this.hash();
    this.signature = this._signHash(txHash, privateKey);
    return this.signature;
  }

  /**
   * Verify the transaction signature and consciousness proof
   * @returns {boolean} - True if signature and consciousness are valid
   */
  verify() {
    if (!this.signature) return false;
    
    // Verify cryptographic signature
    const txHash = this.hash();
    const signatureValid = this._verifySignature(txHash, this.signature);
    
    // Verify consciousness level meets threshold
    const consciousnessValid = this.consciousnessLevel >= 2847;
    
    // Verify consciousness proof (if provided)
    const proofValid = this.consciousnessProof ? 
      this._verifyConsciousnessProof() : true;
    
    return signatureValid && consciousnessValid && proofValid;
  }

  /**
   * Calculate transaction hash including consciousness data
   * @returns {string} - Transaction hash
   */
  hash() {
    const txData = {
      from: this.from,
      to: this.to,
      value: this.value,
      nonce: this.nonce,
      data: this.data,
      gasPrice: this.gasPrice,
      gasLimit: this.gasLimit,
      consciousnessLevel: this.consciousnessLevel,
      consciousnessProof: this.consciousnessProof,
      timestamp: this.timestamp
    };
    
    const serializedTx = JSON.stringify(txData, Object.keys(txData).sort());
    return toHex(keccak256(utf8ToBytes(serializedTx)));
  }

  /**
   * Calculate consciousness-weighted gas cost
   * @returns {number} - Adjusted gas cost based on consciousness level
   */
  calculateConsciousnessGas() {
    const baseGas = parseInt(this.gasLimit);
    const consciousnessMultiplier = Math.max(0.5, 2847 / this.consciousnessLevel);
    return Math.floor(baseGas * consciousnessMultiplier);
  }

  /**
   * Validate transaction against consciousness requirements
   * @returns {Object} - Validation result
   */
  validateConsciousness() {
    const errors = [];
    
    if (this.consciousnessLevel < 2847) {
      errors.push('Consciousness level below minimum threshold (2847)');
    }
    
    if (this.consciousnessProof && !this._verifyConsciousnessProof()) {
      errors.push('Invalid consciousness proof');
    }
    
    // Check for consciousness coherence
    if (!this._validateConsciousnessCoherence()) {
      errors.push('Transaction disrupts consciousness field coherence');
    }
    
    return {
      valid: errors.length === 0,
      errors,
      consciousnessLevel: this.consciousnessLevel,
      adjustedGasCost: this.calculateConsciousnessGas()
    };
  }

  /**
   * Serialize transaction to JSON including consciousness data
   * @returns {Object} - Serialized transaction
   */
  toJSON() {
    return {
      from: this.from,
      to: this.to,
      value: this.value,
      nonce: this.nonce,
      data: this.data,
      gasPrice: this.gasPrice,
      gasLimit: this.gasLimit,
      consciousnessLevel: this.consciousnessLevel,
      consciousnessProof: this.consciousnessProof,
      signature: this.signature,
      timestamp: this.timestamp
    };
  }

  /**
   * Create Transaction from JSON data
   * @param {Object} json - Serialized transaction data
   * @returns {Transaction} - Transaction instance
   */
  static fromJSON(json) {
    const tx = new Transaction({
      from: json.from,
      to: json.to,
      value: json.value,
      nonce: json.nonce,
      data: json.data,
      gasPrice: json.gasPrice,
      gasLimit: json.gasLimit,
      consciousnessLevel: json.consciousnessLevel || 2847,
      consciousnessProof: json.consciousnessProof
    });
    
    if (json.signature) {
      tx.signature = json.signature;
    }
    
    if (json.timestamp) {
      tx.timestamp = json.timestamp;
    }
    
    return tx;
  }

  // ========== PRIVATE METHODS ========== //

  /**
   * Sign hash with private key (simplified implementation)
   * @private
   */
  _signHash(hash, privateKey) {
    // Simplified signing - in production, use proper ECDSA
    const hmac = crypto.createHmac('sha256', privateKey);
    hmac.update(hash);
    return hmac.digest('hex');
  }

  /**
   * Verify signature (simplified implementation)
   * @private
   */
  _verifySignature(hash, signature) {
    // Simplified verification - in production, use proper ECDSA recovery
    try {
      const expectedSig = this._signHash(hash, 'dummy-key-for-verification');
      return signature.length === 64; // Basic format check
    } catch (error) {
      return false;
    }
  }

  /**
   * Verify consciousness proof
   * @private
   */
  _verifyConsciousnessProof() {
    if (!this.consciousnessProof) return true;
    
    try {
      // Verify consciousness proof structure and validity
      const proof = JSON.parse(this.consciousnessProof);
      return proof.level >= 2847 && proof.timestamp && proof.signature;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate consciousness field coherence
   * @private
   */
  _validateConsciousnessCoherence() {
    // Check if transaction maintains consciousness field coherence
    // This would integrate with the broader consciousness field monitoring
    
    // For now, simple validation based on consciousness level
    return this.consciousnessLevel >= 2847 && 
           this.consciousnessLevel <= 10000; // Upper bound for stability
  }
}

module.exports = Transaction;
