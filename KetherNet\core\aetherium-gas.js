const BN = require('bn.js');
const { keccak256 } = require('ethereum-cryptography/keccak');

class AetheriumGas {
  constructor() {
    // Base fee in wei (1 AE = 10^18 wei)
    this.baseFee = new BN('1000000000'); // 1 Gwei
    this.baseFeeMaxChangeDenominator = 8; // 12.5% max change per block
    this.targetGasUsed = new BN('15000000'); // 15M gas target
    this.maxGasLimit = new BN('30000000'); // 30M gas limit
    this.minGasLimit = new BN('5000');
    
    // Storage for pending transactions
    this.pendingTransactions = new Map();
    this.gasPriceOracle = {
      lastUpdate: Date.now(),
      baseFeeHistory: [],
      priorityFeeHistory: [],
      maxHistorySize: 1024
    };
    
    // Initialize with some history
    this._initializeGasPriceOracle();
  }

  /**
   * Initialize gas price oracle with some initial data
   * @private
   */
  _initializeGasPriceOracle() {
    const now = Date.now();
    for (let i = 0; i < 100; i++) {
      this.gasPriceOracle.baseFeeHistory.push({
        blockNumber: i,
        baseFee: this.baseFee.toString(),
        timestamp: now - (100 - i) * 15000 // Simulate 15s blocks
      });
    }
  }

  /**
   * Calculate intrinsic gas for a transaction
   * @param {Object} tx - Transaction object
   * @returns {BN} Gas cost
   */
  calculateIntrinsicGas(tx) {
    // Base cost
    let gas = new BN('21000');
    
    // Add cost for data (4 gas per zero byte, 16 per non-zero byte)
    if (tx.data) {
      for (const byte of tx.data) {
        gas = gas.add(new BN(byte === 0 ? 4 : 16));
      }
    }
    
    // Add cost for contract creation
    if (tx.to === null || tx.to === undefined) {
      gas = gas.add(new BN('32000'));
    }
    
    return gas;
  }

  /**
   * Estimate gas for a transaction
   * @param {Object} tx - Transaction object
   * @returns {Object} Gas estimation result
   */
  async estimateGas(tx) {
    const intrinsicGas = this.calculateIntrinsicGas(tx);
    
    // For simplicity, we'll just return intrinsic gas * 2
    // In a real implementation, this would execute the transaction in a VM
    const estimatedGas = intrinsicGas.muln(2);
    
    return {
      gasUsed: parseInt(estimatedGas.toString()),
      baseFeePerGas: parseInt(this.baseFee.toString()),
      maxPriorityFeePerGas: parseInt(this._calculateSuggestedPriorityFee())
    };
  }

  /**
   * Get current gas price information
   * @returns {Object} Gas price info
   */
  getGasPrice() {
    return {
      baseFee: this.baseFee.toString(),
      maxPriorityFee: this._calculateSuggestedPriorityFee(),
      maxFee: this.baseFee.muln(2).toString(),
      lastUpdated: this.gasPriceOracle.lastUpdate
    };
  }

  /**
   * Update base fee based on parent block gas usage
   * @param {BN} parentGasUsed - Gas used in parent block
   */
  updateBaseFee(parentGasUsed) {
    const parentGasTarget = this.targetGasUsed;
    const parentBaseFee = this.baseFee;
    
    if (parentGasUsed.eq(parentGasTarget)) {
      // No change if target was hit exactly
      return parentBaseFee;
    }
    
    let baseFeeChange;
    if (parentGasUsed.gt(parentGasTarget)) {
      // If parent block used more gas than target, increase base fee
      const gasUsedDelta = parentGasUsed.sub(parentGasTarget);
      const baseFeeDelta = parentBaseFee.mul(gasUsedDelta)
        .div(parentGasTarget)
        .divn(this.baseFeeMaxChangeDenominator);
      baseFeeChange = baseFeeDelta;
    } else {
      // If parent block used less gas than target, decrease base fee
      const gasUsedDelta = parentGasTarget.sub(parentGasUsed);
      const baseFeeDelta = parentBaseFee.mul(gasUsedDelta)
        .div(parentGasTarget)
        .divn(this.baseFeeMaxChangeDenominator);
      baseFeeChange = baseFeeDelta.neg();
    }
    
    // Update base fee (can't go below minimum)
    const newBaseFee = BN.max(
      parentBaseFee.add(baseFeeChange),
      new BN('1000000000') // 1 Gwei minimum
    );
    
    this.baseFee = newBaseFee;
    
    // Update oracle history
    this._updateGasPriceOracle(newBaseFee);
    
    return newBaseFee;
  }

  /**
   * Validate transaction gas parameters
   * @param {Object} tx - Transaction to validate
   * @param {BN} senderBalance - Sender's AE balance
   * @returns {Object} Validation result
   */
  validateTransaction(tx, senderBalance) {
    const intrinsicGas = this.calculateIntrinsicGas(tx);
    const maxFeePerGas = new BN(tx.maxFeePerGas || '0');
    const maxPriorityFee = new BN(tx.maxPriorityFeePerGas || '0');
    const gasLimit = new BN(tx.gasLimit || '0');
    
    // Check gas limit is above intrinsic gas
    if (gasLimit.lt(intrinsicGas)) {
      return { valid: false, reason: 'INTRINSIC_GAS_TOO_LOW' };
    }
    
    // Check max fee is sufficient
    if (maxFeePerGas.lt(this.baseFee)) {
      return { valid: false, reason: 'MAX_FEE_BELOW_BASE_FEE' };
    }
    
    // Check max priority fee is reasonable
    if (maxPriorityFee.gt(maxFeePerGas)) {
      return { valid: false, reason: 'MAX_PRIORITY_FEE_ABOVE_MAX_FEE' };
    }
    
    // Calculate max cost
    const maxCost = maxFeePerGas.mul(gasLimit);
    
    // Check sender can afford the transaction
    if (maxCost.gt(senderBalance)) {
      return { valid: false, reason: 'INSUFFICIENT_BALANCE' };
    }
    
    return { valid: true };
  }

  /**
   * Calculate suggested priority fee based on recent blocks
   * @private
   * @returns {string} Suggested priority fee in wei
   */
  _calculateSuggestedPriorityFee() {
    // In a real implementation, this would analyze recent blocks
    // For now, we'll return a fixed fraction of the base fee
    return this.baseFee.divn(4).toString();
  }

  /**
   * Update gas price oracle with new base fee
   * @private
   * @param {BN} baseFee - New base fee
   */
  _updateGasPriceOracle(baseFee) {
    const now = Date.now();
    const blockNumber = this.gasPriceOracle.baseFeeHistory.length > 0
      ? this.gasPriceOracle.baseFeeHistory[this.gasPriceOracle.baseFeeHistory.length - 1].blockNumber + 1
      : 0;
    
    this.gasPriceOracle.baseFeeHistory.push({
      blockNumber,
      baseFee: baseFee.toString(),
      timestamp: now
    });
    
    // Trim history if needed
    if (this.gasPriceOracle.baseFeeHistory.length > this.gasPriceOracle.maxHistorySize) {
      this.gasPriceOracle.baseFeeHistory.shift();
    }
    
    this.gasPriceOracle.lastUpdate = now;
  }

  /**
   * Get base fee history
   * @param {number} blocks - Number of blocks to return
   * @returns {Array} Base fee history
   */
  getBaseFeeHistory(blocks = 20) {
    const history = [...this.gasPriceOracle.baseFeeHistory];
    return history.slice(-Math.min(blocks, history.length));
  }
}

module.exports = AetheriumGas;
