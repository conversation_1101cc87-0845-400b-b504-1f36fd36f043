// Use a safe reference to globalThis that works in all environments
const globalObj = typeof globalThis !== 'undefined' ? globalThis : window || global;

// Use global BigInt if available, otherwise fall back to a polyfill
const BigInt = globalObj.BigInt || require('big-integer');

// Import Transaction class
const Transaction = require('./transaction');

/**
 * KetherNet Transaction Pool with Consciousness Prioritization
 * Manages pending transactions with consciousness-aware ordering and validation
 */
class TransactionPool {
  constructor() {
    this.pool = new Map(); // txHash -> transaction
    this.pendingNonces = new Map(); // address -> nonce
    this.consciousnessQueue = new Map(); // consciousnessLevel -> Set<txHash>
    this.maxPoolSize = 10000;
    this.consciousnessThreshold = 2847;
  }

  /**
   * Add a transaction to the pool with consciousness validation
   * @param {Transaction} transaction - Transaction to add
   * @returns {boolean} - True if added, false if rejected
   */
  add(transaction) {
    // Verify transaction including consciousness validation
    if (!this._isValidTransaction(transaction)) {
      return false;
    }

    const txHash = transaction.hash();
    if (!txHash) {
      console.error('Transaction missing hash method');
      return false;
    }
    
    // Check if transaction already exists
    if (this.pool.has(txHash)) {
      return false;
    }

    // Check pool size limit
    if (this.pool.size >= this.maxPoolSize) {
      if (!this._evictLowConsciousnessTransaction(transaction.consciousnessLevel)) {
        return false; // Pool full and no evictable transactions
      }
    }

    // Add to pool
    this.pool.set(txHash, transaction);
    
    // Add to consciousness queue for prioritization
    this._addToConsciousnessQueue(txHash, transaction.consciousnessLevel);
    
    // Update pending nonce for sender
    this._updatePendingNonce(transaction.from, transaction.nonce.toString());
    
    return true;
  }

  /**
   * Get a transaction by hash
   * @param {string} txHash - Transaction hash
   * @returns {Transaction|undefined} - Transaction if found
   */
  get(txHash) {
    return this.pool.get(txHash);
  }

  /**
   * Get all transactions in the pool
   * @returns {Array<Transaction>} - Array of transactions
   */
  getAll() {
    return Array.from(this.pool.values());
  }

  /**
   * Get transactions ordered by consciousness level (highest first)
   * @param {number} limit - Maximum number of transactions to return
   * @returns {Array<Transaction>} - Array of transactions ordered by consciousness
   */
  getByConsciousnessOrder(limit = 100) {
    const transactions = [];
    const consciousnessLevels = Array.from(this.consciousnessQueue.keys())
      .sort((a, b) => b - a); // Highest consciousness first

    for (const level of consciousnessLevels) {
      const txHashes = this.consciousnessQueue.get(level);
      if (txHashes) {
        for (const txHash of txHashes) {
          const tx = this.pool.get(txHash);
          if (tx) {
            transactions.push(tx);
            if (transactions.length >= limit) {
              return transactions;
            }
          }
        }
      }
    }

    return transactions;
  }

  /**
   * Get transactions by sender
   * @param {string} address - Sender address
   * @returns {Array<Transaction>} - Array of transactions
   */
  getBySender(address) {
    return this.getAll().filter(tx => tx.from.toLowerCase() === address.toLowerCase());
  }

  /**
   * Get transactions above consciousness threshold
   * @param {number} threshold - Minimum consciousness level (default: 2847)
   * @returns {Array<Transaction>} - Array of high-consciousness transactions
   */
  getHighConsciousnessTransactions(threshold = this.consciousnessThreshold) {
    return this.getAll().filter(tx => tx.consciousnessLevel >= threshold);
  }

  /**
   * Remove transactions from the pool
   * @param {Array<string>} txHashes - Array of transaction hashes to remove
   */
  remove(txHashes) {
    txHashes.forEach(hash => {
      const tx = this.pool.get(hash);
      if (tx) {
        this.pool.delete(hash);
        this._removeFromConsciousnessQueue(hash, tx.consciousnessLevel);
      }
    });
  }

  /**
   * Clear the transaction pool
   */
  clear() {
    this.pool.clear();
    this.pendingNonces.clear();
    this.consciousnessQueue.clear();
  }

  /**
   * Get the next nonce for an address
   * @param {string} address - Account address
   * @param {number} currentNonce - Current nonce from account state
   * @returns {string} - Next nonce as a string
   */
  getNextNonce(address, currentNonce = '0') {
    const pendingNonce = this.pendingNonces.get(address.toLowerCase()) || '0';
    const currentNonceBN = BigInt(currentNonce.toString());
    const pendingNonceBN = BigInt(pendingNonce.toString());
    
    // Return the higher nonce + 1
    const nextNonce = (pendingNonceBN > currentNonceBN 
      ? pendingNonceBN 
      : currentNonceBN) + BigInt(1);
      
    return nextNonce.toString();
  }

  /**
   * Get pool statistics including consciousness metrics
   * @returns {Object} - Pool statistics
   */
  getStats() {
    const transactions = this.getAll();
    const consciousnessLevels = transactions.map(tx => tx.consciousnessLevel);
    
    return {
      totalTransactions: transactions.length,
      maxPoolSize: this.maxPoolSize,
      averageConsciousness: consciousnessLevels.length > 0 
        ? consciousnessLevels.reduce((a, b) => a + b, 0) / consciousnessLevels.length 
        : 0,
      highConsciousnessCount: transactions.filter(tx => tx.consciousnessLevel >= this.consciousnessThreshold).length,
      consciousnessDistribution: this._getConsciousnessDistribution(),
      pendingAddresses: this.pendingNonces.size
    };
  }

  // ========== PRIVATE METHODS ========== //

  /**
   * Check if a transaction is valid including consciousness validation
   * @private
   * @param {Transaction} transaction - Transaction to validate
   * @returns {boolean} - True if valid
   */
  _isValidTransaction(transaction) {
    // Basic validation
    if (!transaction.from || !transaction.to || !transaction.value || !transaction.nonce) {
      return false;
    }

    // Verify signature and consciousness
    if (!transaction.verify()) {
      return false;
    }

    // Validate consciousness requirements
    const consciousnessValidation = transaction.validateConsciousness();
    if (!consciousnessValidation.valid) {
      console.warn('Transaction failed consciousness validation:', consciousnessValidation.errors);
      return false;
    }

    // Check nonce is not too far in the future
    const currentNonce = this.pendingNonces.get(transaction.from.toLowerCase()) || '0';
    const txNonce = BigInt(transaction.nonce.toString());
    const maxFutureNonce = BigInt(currentNonce.toString()) + BigInt(100);
    
    if (txNonce > maxFutureNonce) {
      return false;
    }

    return true;
  }

  /**
   * Add transaction to consciousness-based priority queue
   * @private
   */
  _addToConsciousnessQueue(txHash, consciousnessLevel) {
    if (!this.consciousnessQueue.has(consciousnessLevel)) {
      this.consciousnessQueue.set(consciousnessLevel, new Set());
    }
    this.consciousnessQueue.get(consciousnessLevel).add(txHash);
  }

  /**
   * Remove transaction from consciousness-based priority queue
   * @private
   */
  _removeFromConsciousnessQueue(txHash, consciousnessLevel) {
    const levelQueue = this.consciousnessQueue.get(consciousnessLevel);
    if (levelQueue) {
      levelQueue.delete(txHash);
      if (levelQueue.size === 0) {
        this.consciousnessQueue.delete(consciousnessLevel);
      }
    }
  }

  /**
   * Evict low consciousness transaction to make room for higher consciousness one
   * @private
   */
  _evictLowConsciousnessTransaction(newTxConsciousness) {
    // Find the lowest consciousness level in the pool
    const consciousnessLevels = Array.from(this.consciousnessQueue.keys()).sort((a, b) => a - b);
    
    for (const level of consciousnessLevels) {
      if (level < newTxConsciousness) {
        const levelQueue = this.consciousnessQueue.get(level);
        if (levelQueue && levelQueue.size > 0) {
          const txHashToEvict = levelQueue.values().next().value;
          this.remove([txHashToEvict]);
          return true;
        }
      }
    }
    
    return false; // No evictable transactions found
  }

  /**
   * Get consciousness level distribution
   * @private
   */
  _getConsciousnessDistribution() {
    const distribution = {};
    for (const [level, txHashes] of this.consciousnessQueue.entries()) {
      distribution[level] = txHashes.size;
    }
    return distribution;
  }

  /**
   * Update the pending nonce for an address
   * @private
   * @param {string} address - Account address
   * @param {string} nonce - Transaction nonce
   */
  _updatePendingNonce(address, nonce) {
    const currentNonce = this.pendingNonces.get(address.toLowerCase()) || '0';
    const newNonce = BigInt(nonce.toString());
    const currentNonceBN = BigInt(currentNonce.toString());
    
    if (newNonce > currentNonceBN) {
      this.pendingNonces.set(address.toLowerCase(), newNonce.toString());
    }
  }
}

module.exports = TransactionPool;
