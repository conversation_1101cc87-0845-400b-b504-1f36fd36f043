const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const BN = require('bn.js');
const AetheriumGas = require('./aetherium-gas');
const app = express();

app.use(cors());
app.use(express.json());

// In-memory stores (in production, use a database)
const crownNodes = new Map();
const coheriumLedger = new Map();
const pendingRewards = new Map();
const aetheriumAccounts = new Map(); // AE token accounts

// Initialize Aetherium Gas System
const aetheriumGas = new AetheriumGas();

// Initialize genesis account with 1M AE
aetheriumAccounts.set('******************************************', {
  balance: new BN('1000000000000000000000000'), // 1M AE (18 decimals)
  nonce: 0
});

// Constants from KetherNet specs
const CONSCIOUSNESS_THRESHOLD = 2847;
const MAX_COHERIUM_SUPPLY = *********; // 144M tokens
const CROWN_NODE_THRESHOLD = 0.18; // 18% of total nodes become Crown Nodes
const BLOCK_REWARD = 100; // Coherium tokens per block
const GENESIS_ADDRESS = '0000000000000000000000000000000000000000';
const GENESIS_REWARD = 1000000; // Initial supply for the genesis block

// Initialize genesis block
coheriumLedger.set(GENESIS_ADDRESS, {
  balance: GENESIS_REWARD,
  lastClaim: 0,
  totalEarned: GENESIS_REWARD,
  address: GENESIS_ADDRESS
});

class CoheriumManager {
  constructor() {
    this.totalSupply = GENESIS_REWARD;
    this.blockHeight = 0;
    this.lastRewardTime = Date.now();
  }

  getNodeAddress(nodeId) {
    return crypto.createHash('sha256').update(`node:${nodeId}`).digest('hex');
  }

  initializeNode(nodeId) {
    const address = this.getNodeAddress(nodeId);
    const nodeAccount = {
      balance: 0,
      lastClaim: Date.now(),
      totalEarned: 0,
      address: address
    };
    coheriumLedger.set(address, nodeAccount);
    return nodeAccount;
  }

  distributeRewards() {
    if (crownNodes.size === 0) return;

    const totalScore = Array.from(crownNodes.values()).reduce((sum, node) => sum + node.score, 0);
    if (totalScore === 0) return;

    const blockReward = BLOCK_REWARD;
    
    crownNodes.forEach(node => {
      const nodeShare = node.score / totalScore;
      const rewardAmount = Math.floor(blockReward * nodeShare);
      
      if (rewardAmount > 0) {
        const address = this.getNodeAddress(node.id);
        const nodeAccount = coheriumLedger.get(address) || this.initializeNode(node.id);
        
        // Add to pending rewards
        pendingRewards.set(node.id, (pendingRewards.get(node.id) || 0) + rewardAmount);
        
        // Update total supply
        this.totalSupply += rewardAmount;
        this.blockHeight++;
        this.lastRewardTime = Date.now();
      }
    });
  }

  claimRewards(nodeId) {
    const pendingAmount = pendingRewards.get(nodeId) || 0;
    if (pendingAmount > 0) {
      const address = this.getNodeAddress(nodeId);
      const nodeAccount = coheriumLedger.get(address) || this.initializeNode(nodeId);
      
      nodeAccount.balance += pendingAmount;
      nodeAccount.totalEarned += pendingAmount;
      nodeAccount.lastClaim = Date.now();
      
      coheriumLedger.set(address, nodeAccount);
      pendingRewards.delete(nodeId);
      
      return pendingAmount;
    }
    return 0;
  }
}

const coheriumManager = new CoheriumManager();

// Consciousness validation middleware
function validateConsciousness(req, res, next) {
  const { nodeId, level, validation } = req.body;
  
  if (!nodeId || !level || !validation) {
    return res.status(400).json({ 
      error: 'Missing consciousness parameters',
      required: ['nodeId', 'level', 'validation']
    });
  }

  // Enhanced consciousness validation
  const consciousnessScore = calculateConsciousnessScore(level, validation);
  
  if (consciousnessScore < CONSCIOUSNESS_THRESHOLD) {
    return res.status(403).json({ 
      error: 'Insufficient consciousness level',
      required: CONSCIOUSNESS_THRESHOLD,
      provided: consciousnessScore
    });
  }

  req.consciousness = { nodeId, level: consciousnessScore, validation };
  next();
}

function calculateConsciousnessScore(level, validation) {
  // Implement UUFT-based consciousness calculation
  const baseScore = parseFloat(level) || 0;
  const validationBonus = validation ? 500 : 0;
  const coherenceMultiplier = 1.2;
  
  return Math.floor((baseScore + validationBonus) * coherenceMultiplier);
}

// Crown Node management
function updateCrownNodes() {
  const allNodes = Array.from(crownNodes.values());
  const sortedNodes = allNodes.sort((a, b) => b.score - a.score);
  const crownNodeCount = Math.max(1, Math.floor(allNodes.length * CROWN_NODE_THRESHOLD));
  
  // Clear existing crown status
  crownNodes.forEach(node => node.isCrown = false);
  
  // Assign crown status to top nodes
  for (let i = 0; i < Math.min(crownNodeCount, sortedNodes.length); i++) {
    sortedNodes[i].isCrown = true;
  }
  
  return crownNodeCount;
}

// API Endpoints

// Register a consciousness node
app.post('/register-node', validateConsciousness, (req, res) => {
  const { nodeId, level } = req.consciousness;
  
  const node = {
    id: nodeId,
    score: level,
    lastSeen: Date.now(),
    isCrown: false,
    address: coheriumManager.getNodeAddress(nodeId)
  };
  
  crownNodes.set(nodeId, node);
  updateCrownNodes();
  
  res.json({
    success: true,
    node: node,
    crownNodes: Array.from(crownNodes.values()).filter(n => n.isCrown).length
  });
});

// Enhanced Crown Consensus Endpoint
app.post('/crown-consensus', async (req, res) => {
  try {
    const { content_id, transaction_data, gasOptions } = req.body;
    const { nodeId, level, validation } = req.consciousness;
    
    // Process gas payment if gasOptions are provided
    if (gasOptions) {
      const { from, maxFeePerGas, maxPriorityFeePerGas } = gasOptions;
      const gasEstimate = await aetheriumGas.estimateGas({
        from,
        to: null, // Consensus contract address would go here
        data: JSON.stringify({ content_id, ...transaction_data })
      });
      
      const gasUsed = gasEstimate.gasUsed;
      const effectiveGasPrice = Math.min(maxFeePerGas, gasEstimate.baseFeePerGas + maxPriorityFeePerGas);
      const totalGasCost = gasUsed * effectiveGasPrice;
      
      // Deduct gas from sender's account
      const senderAccount = aetheriumAccounts.get(from);
      if (!senderAccount || senderAccount.balance.lt(new BN(totalGasCost.toString()))) {
        return res.status(400).json({
          success: false,
          error: 'Insufficient AE balance for gas',
          required: totalGasCost,
          available: senderAccount ? senderAccount.balance.toString() : '0'
        });
      }
      
      senderAccount.balance = senderAccount.balance.sub(new BN(totalGasCost.toString()));
      senderAccount.nonce++;
    }
    
    // Crown consensus logic
    const crownNodesList = Array.from(crownNodes.values()).filter(n => n.isCrown);
    const consensusResult = {
      content_id,
      consensus_reached: true,
      crown_nodes_participating: crownNodesList.length,
      consciousness_threshold_met: level >= CONSCIOUSNESS_THRESHOLD,
      timestamp: Date.now(),
      block_height: coheriumManager.blockHeight
    };
    
    // Distribute rewards to crown nodes
    coheriumManager.distributeRewards();
    
    res.json({
      success: true,
      result: consensusResult,
      gas_used: gasOptions ? gasEstimate.gasUsed : 0
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get Coherium balance
app.get('/coherium/balance/:nodeId', (req, res) => {
  const { nodeId } = req.params;
  const address = coheriumManager.getNodeAddress(nodeId);
  const account = coheriumLedger.get(address);
  const pending = pendingRewards.get(nodeId) || 0;
  
  res.json({
    success: true,
    nodeId,
    address,
    balance: account ? account.balance : 0,
    pending_rewards: pending,
    total_earned: account ? account.totalEarned : 0,
    last_claim: account ? account.lastClaim : null
  });
});

// Claim Coherium rewards
app.post('/coherium/claim', validateConsciousness, (req, res) => {
  const { nodeId } = req.consciousness;
  const claimedAmount = coheriumManager.claimRewards(nodeId);
  
  res.json({
    success: true,
    nodeId,
    claimed_amount: claimedAmount,
    timestamp: Date.now()
  });
});

// Get Coherium network stats
app.get('/coherium/network', (req, res) => {
  res.json({
    success: true,
    stats: {
      total_supply: coheriumManager.totalSupply,
      max_supply: MAX_COHERIUM_SUPPLY,
      remaining_supply: MAX_COHERIUM_SUPPLY - coheriumManager.totalSupply,
      block_height: coheriumManager.blockHeight,
      block_reward: BLOCK_REWARD,
      crown_nodes: Array.from(crownNodes.values()).filter(n => n.isCrown).length,
      active_nodes: crownNodes.size,
      last_reward: coheriumManager.lastRewardTime,
      timestamp: Date.now()
    }
  });
});

const PORT = process.env.PORT || 8080;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('🔗 KetherNet Blockchain running on port', PORT);
  console.log('💎 Coherium validation active');
  console.log('👑 Crown Consensus enabled');
  console.log('💰 Coherium supply:', coheriumManager.totalSupply, '/', MAX_COHERIUM_SUPPLY);
});

module.exports = { app, server, coheriumManager };
