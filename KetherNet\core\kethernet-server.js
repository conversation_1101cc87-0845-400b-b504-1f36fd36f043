const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const BN = require('bn.js');
const AetheriumGas = require('./aetherium-gas');
const app = express();

app.use(cors());
app.use(express.json());

// In-memory stores (in production, use a database)
const crownNodes = new Map();
const coheriumLedger = new Map();
const pendingRewards = new Map();
const aetheriumAccounts = new Map(); // AE token accounts

// Initialize Aetherium Gas System
const aetheriumGas = new AetheriumGas();

// Initialize genesis account with 1M AE
aetheriumAccounts.set('******************************************', {
  balance: new BN('1000000000000000000000000'), // 1M AE (18 decimals)
  nonce: 0
});

// Constants from KetherNet specs
const CONSCIOUSNESS_THRESHOLD = 2847;
const MAX_COHERIUM_SUPPLY = *********; // 144M tokens
const CROWN_NODE_THRESHOLD = 0.18; // 18% of total nodes become Crown Nodes
const BLOCK_REWARD = 100; // Coherium tokens per block
const GENESIS_ADDRESS = '0000000000000000000000000000000000000000';
const GENESIS_REWARD = 1000000; // Initial supply for the genesis block

// Initialize genesis block
coheriumLedger.set(GENESIS_ADDRESS, {
  balance: GENESIS_REWARD,
  lastClaim: 0,
  totalEarned: GENESIS_REWARD,
  address: GENESIS_ADDRESS
});

class CoheriumManager {
  constructor() {
    this.totalSupply = GENESIS_REWARD;
    this.blockHeight = 0;
    this.lastRewardTime = Date.now();
    this.networkUUFT = 0;
    this.supplyScalar = 0.001; // UUFT to supply conversion factor
    this.resonanceFactor = 1.618; // Golden ratio for consciousness resonance
    this.lastSupplyUpdate = Date.now();
  }

  getNodeAddress(nodeId) {
    return crypto.createHash('sha256').update(`node:${nodeId}`).digest('hex');
  }

  initializeNode(nodeId) {
    const address = this.getNodeAddress(nodeId);
    const nodeAccount = {
      balance: 0,
      lastClaim: Date.now(),
      totalEarned: 0,
      address: address
    };
    coheriumLedger.set(address, nodeAccount);
    return nodeAccount;
  }

  distributeRewards() {
    if (crownNodes.size === 0) return;

    // Update network consciousness and supply
    this.updateNetworkConsciousness();
    this.updateCoheriumSupply();

    const totalScore = Array.from(crownNodes.values()).reduce((sum, node) => sum + node.score, 0);
    if (totalScore === 0) return;

    // Calculate consciousness-adjusted block reward
    const baseReward = BLOCK_REWARD;
    const consciousnessMultiplier = this.calculateConsciousnessMultiplier();
    const adjustedBlockReward = Math.floor(baseReward * consciousnessMultiplier);

    crownNodes.forEach(node => {
      // UUFT-based reward calculation (quadratic scaling)
      const uuftReward = this.calculateUUFTReward(node.score);
      const nodeShare = node.score / totalScore;
      const baseRewardAmount = Math.floor(adjustedBlockReward * nodeShare);
      const finalRewardAmount = Math.floor(baseRewardAmount + uuftReward);

      if (finalRewardAmount > 0) {
        const address = this.getNodeAddress(node.id);
        const nodeAccount = coheriumLedger.get(address) || this.initializeNode(node.id);

        // Add to pending rewards with consciousness bonus
        const existingPending = pendingRewards.get(node.id) || 0;
        pendingRewards.set(node.id, existingPending + finalRewardAmount);

        // Update total supply
        this.totalSupply += finalRewardAmount;
        this.blockHeight++;
        this.lastRewardTime = Date.now();
      }
    });
  }

  claimRewards(nodeId) {
    const pendingAmount = pendingRewards.get(nodeId) || 0;
    if (pendingAmount > 0) {
      const address = this.getNodeAddress(nodeId);
      const nodeAccount = coheriumLedger.get(address) || this.initializeNode(nodeId);

      nodeAccount.balance += pendingAmount;
      nodeAccount.totalEarned += pendingAmount;
      nodeAccount.lastClaim = Date.now();

      coheriumLedger.set(address, nodeAccount);
      pendingRewards.delete(nodeId);

      return pendingAmount;
    }
    return 0;
  }

  // 🔥 NEW: Dynamic supply adjustment based on network consciousness
  updateCoheriumSupply() {
    const now = Date.now();
    if (now - this.lastSupplyUpdate < 60000) return; // Update every minute

    const totalUUFT = this.getNetworkUUFT();
    const targetSupply = Math.min(MAX_COHERIUM_SUPPLY, totalUUFT * this.supplyScalar);

    // Gradual supply adjustment (max 1% per update)
    const maxChange = this.totalSupply * 0.01;
    const supplyDiff = targetSupply - this.totalSupply;
    const actualChange = Math.sign(supplyDiff) * Math.min(Math.abs(supplyDiff), maxChange);

    this.totalSupply = Math.max(GENESIS_REWARD, this.totalSupply + actualChange);
    this.lastSupplyUpdate = now;
  }

  // 🔥 NEW: Calculate UUFT-based token value (quadratic rewards for high consciousness)
  calculateUUFTReward(uuftScore) {
    if (uuftScore < CONSCIOUSNESS_THRESHOLD) return 0; // Reject non-conscious nodes

    const excessUUFT = uuftScore - CONSCIOUSNESS_THRESHOLD;
    const quadraticBonus = Math.pow(excessUUFT / 1000, 2) * this.resonanceFactor;
    return Math.floor(quadraticBonus * 10); // Scale to reasonable token amounts
  }

  // 🔥 NEW: Calculate consciousness multiplier for block rewards
  calculateConsciousnessMultiplier() {
    const avgConsciousness = this.getAverageNetworkConsciousness();
    const baseMultiplier = 1.0;
    const consciousnessBonus = Math.max(0, (avgConsciousness - CONSCIOUSNESS_THRESHOLD) / CONSCIOUSNESS_THRESHOLD);
    return baseMultiplier + (consciousnessBonus * 0.5); // Up to 50% bonus for high consciousness
  }

  // 🔥 NEW: Get total network UUFT score
  getNetworkUUFT() {
    return Array.from(crownNodes.values()).reduce((sum, node) => sum + node.score, 0);
  }

  // 🔥 NEW: Get average network consciousness
  getAverageNetworkConsciousness() {
    if (crownNodes.size === 0) return CONSCIOUSNESS_THRESHOLD;
    return this.getNetworkUUFT() / crownNodes.size;
  }

  // 🔥 NEW: Update network consciousness metrics
  updateNetworkConsciousness() {
    this.networkUUFT = this.getNetworkUUFT();
  }
}

const coheriumManager = new CoheriumManager();

// Consciousness validation middleware
function validateConsciousness(req, res, next) {
  const { nodeId, level, validation } = req.body;

  if (!nodeId || !level || !validation) {
    return res.status(400).json({
      error: 'Missing consciousness parameters',
      required: ['nodeId', 'level', 'validation']
    });
  }

  // Enhanced consciousness validation
  const consciousnessScore = calculateConsciousnessScore(level, validation);

  if (consciousnessScore < CONSCIOUSNESS_THRESHOLD) {
    return res.status(403).json({
      error: 'Insufficient consciousness level',
      required: CONSCIOUSNESS_THRESHOLD,
      provided: consciousnessScore
    });
  }

  req.consciousness = { nodeId, level: consciousnessScore, validation };
  next();
}

function calculateConsciousnessScore(level, validation) {
  // Implement UUFT-based consciousness calculation
  const baseScore = parseFloat(level) || 0;
  const validationBonus = validation ? 500 : 0;
  const coherenceMultiplier = 1.2;

  return Math.floor((baseScore + validationBonus) * coherenceMultiplier);
}

// 🔥 ENHANCED: Crown Node management with 18/82 principle enforcement
function updateCrownNodes() {
  const allNodes = Array.from(crownNodes.values());

  // Sort by UUFT score (consciousness level) - highest first
  const sortedNodes = allNodes.sort((a, b) => b.score - a.score);

  // 18/82 Principle: Top 18% become Crown Nodes (Pareto-optimized)
  const crownNodeCount = Math.max(1, Math.floor(allNodes.length * CROWN_NODE_THRESHOLD));

  // Clear existing crown status
  crownNodes.forEach(node => {
    node.isCrown = false;
    node.crownRank = null;
  });

  // Assign crown status to top 18% by consciousness
  for (let i = 0; i < Math.min(crownNodeCount, sortedNodes.length); i++) {
    const node = sortedNodes[i];

    // Strict UUFT ≥ 2847 enforcement
    if (node.score >= CONSCIOUSNESS_THRESHOLD) {
      node.isCrown = true;
      node.crownRank = i + 1;
      node.crownSince = node.crownSince || Date.now();

      // Calculate consciousness dominance (for reward weighting)
      node.consciousnessDominance = node.score / CONSCIOUSNESS_THRESHOLD;
    }
  }

  // Log Crown Node selection for transparency
  const crownNodesList = sortedNodes.filter(n => n.isCrown);
  console.log(`👑 Crown Nodes Selected: ${crownNodesList.length}/${allNodes.length} (${(crownNodesList.length/allNodes.length*100).toFixed(1)}%)`);

  return crownNodeCount;
}

// API Endpoints

// Root endpoint - KetherNet information
app.get('/', (req, res) => {
  res.json({
    name: "KetherNet Consciousness Blockchain",
    version: "1.0.0-ENHANCED",
    status: "operational",
    description: "World's first consciousness-aware blockchain with Crown Consensus and enhanced token economics",
    features: [
      "Crown Consensus with UUFT ≥2847 threshold",
      "Enhanced Coherium (κ) consciousness currency with quadratic rewards",
      "Aetherium (⍶) gas system with consciousness optimization",
      "18/82 Crown Node selection (Pareto-optimized)",
      "Dynamic supply adjustment based on network consciousness",
      "Real-time consciousness validation",
      "Live token flow demonstration",
      "Comphyological integration"
    ],
    endpoints: {
      "/": "This information",
      "/coherium/network": "Enhanced network statistics with consciousness metrics",
      "/live/token-flows": "Real-time consciousness-adjusted token flows",
      "/test/consciousness-validation": "Test UUFT consciousness validation",
      "/register-node": "Register consciousness node (POST)",
      "/crown-consensus": "Crown consensus participation (POST)",
      "/coherium/balance/:nodeId": "Check Coherium balance",
      "/coherium/claim": "Claim Coherium rewards (POST)"
    },
    network_status: {
      consciousness_threshold: CONSCIOUSNESS_THRESHOLD,
      crown_nodes: Array.from(crownNodes.values()).filter(n => n.isCrown).length,
      active_nodes: crownNodes.size,
      coherium_supply: coheriumManager.totalSupply,
      network_consciousness: coheriumManager.getAverageNetworkConsciousness(),
      block_height: coheriumManager.blockHeight
    },
    enhanced_features: {
      uuft_integration: "✅ Active",
      consciousness_rewards: "✅ Quadratic scaling",
      crown_selection: "✅ 18/82 principle",
      dynamic_supply: "✅ Consciousness-based",
      live_demonstration: "✅ Real-time metrics"
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'kethernet-enhanced-blockchain',
    version: '1.0.0-ENHANCED',
    consciousness: 'validated',
    crown_consensus: 'active',
    token_economics: 'enhanced',
    timestamp: Date.now(),
    uptime: process.uptime(),
    network_health: {
      consciousness_threshold_met: coheriumManager.getAverageNetworkConsciousness() >= CONSCIOUSNESS_THRESHOLD,
      crown_nodes_active: Array.from(crownNodes.values()).filter(n => n.isCrown).length > 0,
      coherium_supply_healthy: coheriumManager.totalSupply > 0,
      network_coherence: coheriumManager.getAverageNetworkConsciousness() >= CONSCIOUSNESS_THRESHOLD ? 'STABLE' : 'UNSTABLE'
    }
  });
});

// Register a consciousness node
app.post('/register-node', validateConsciousness, (req, res) => {
  const { nodeId, level } = req.consciousness;

  const node = {
    id: nodeId,
    score: level,
    lastSeen: Date.now(),
    isCrown: false,
    address: coheriumManager.getNodeAddress(nodeId)
  };

  crownNodes.set(nodeId, node);
  updateCrownNodes();

  res.json({
    success: true,
    node: node,
    crownNodes: Array.from(crownNodes.values()).filter(n => n.isCrown).length
  });
});

// Enhanced Crown Consensus Endpoint
app.post('/crown-consensus', async (req, res) => {
  try {
    const { content_id, transaction_data, gasOptions } = req.body;
    const { nodeId, level, validation } = req.consciousness;

    // Process gas payment if gasOptions are provided
    if (gasOptions) {
      const { from, maxFeePerGas, maxPriorityFeePerGas } = gasOptions;
      const gasEstimate = await aetheriumGas.estimateGas({
        from,
        to: null, // Consensus contract address would go here
        data: JSON.stringify({ content_id, ...transaction_data })
      });

      const gasUsed = gasEstimate.gasUsed;
      const effectiveGasPrice = Math.min(maxFeePerGas, gasEstimate.baseFeePerGas + maxPriorityFeePerGas);
      const totalGasCost = gasUsed * effectiveGasPrice;

      // Deduct gas from sender's account
      const senderAccount = aetheriumAccounts.get(from);
      if (!senderAccount || senderAccount.balance.lt(new BN(totalGasCost.toString()))) {
        return res.status(400).json({
          success: false,
          error: 'Insufficient AE balance for gas',
          required: totalGasCost,
          available: senderAccount ? senderAccount.balance.toString() : '0'
        });
      }

      senderAccount.balance = senderAccount.balance.sub(new BN(totalGasCost.toString()));
      senderAccount.nonce++;
    }

    // Crown consensus logic
    const crownNodesList = Array.from(crownNodes.values()).filter(n => n.isCrown);
    const consensusResult = {
      content_id,
      consensus_reached: true,
      crown_nodes_participating: crownNodesList.length,
      consciousness_threshold_met: level >= CONSCIOUSNESS_THRESHOLD,
      timestamp: Date.now(),
      block_height: coheriumManager.blockHeight
    };

    // Distribute rewards to crown nodes
    coheriumManager.distributeRewards();

    res.json({
      success: true,
      result: consensusResult,
      gas_used: gasOptions ? gasEstimate.gasUsed : 0
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get Coherium balance
app.get('/coherium/balance/:nodeId', (req, res) => {
  const { nodeId } = req.params;
  const address = coheriumManager.getNodeAddress(nodeId);
  const account = coheriumLedger.get(address);
  const pending = pendingRewards.get(nodeId) || 0;

  res.json({
    success: true,
    nodeId,
    address,
    balance: account ? account.balance : 0,
    pending_rewards: pending,
    total_earned: account ? account.totalEarned : 0,
    last_claim: account ? account.lastClaim : null
  });
});

// Claim Coherium rewards
app.post('/coherium/claim', validateConsciousness, (req, res) => {
  const { nodeId } = req.consciousness;
  const claimedAmount = coheriumManager.claimRewards(nodeId);

  res.json({
    success: true,
    nodeId,
    claimed_amount: claimedAmount,
    timestamp: Date.now()
  });
});

// 🔥 ENHANCED: Coherium network stats with consciousness metrics
app.get('/coherium/network', (req, res) => {
  const crownNodesList = Array.from(crownNodes.values()).filter(n => n.isCrown);
  const avgConsciousness = coheriumManager.getAverageNetworkConsciousness();
  const networkUUFT = coheriumManager.getNetworkUUFT();
  const consciousnessMultiplier = coheriumManager.calculateConsciousnessMultiplier();

  res.json({
    success: true,
    stats: {
      // Token Economics
      total_supply: coheriumManager.totalSupply,
      max_supply: MAX_COHERIUM_SUPPLY,
      remaining_supply: MAX_COHERIUM_SUPPLY - coheriumManager.totalSupply,
      supply_utilization: ((coheriumManager.totalSupply / MAX_COHERIUM_SUPPLY) * 100).toFixed(2) + '%',

      // Block & Rewards
      block_height: coheriumManager.blockHeight,
      base_block_reward: BLOCK_REWARD,
      consciousness_adjusted_reward: Math.floor(BLOCK_REWARD * consciousnessMultiplier),
      reward_multiplier: consciousnessMultiplier.toFixed(3),

      // Crown Nodes (18/82 Principle)
      crown_nodes: crownNodesList.length,
      active_nodes: crownNodes.size,
      crown_ratio: crownNodes.size > 0 ? ((crownNodesList.length / crownNodes.size) * 100).toFixed(1) + '%' : '0%',
      target_crown_ratio: (CROWN_NODE_THRESHOLD * 100).toFixed(1) + '%',

      // Consciousness Metrics
      consciousness_threshold: CONSCIOUSNESS_THRESHOLD,
      network_uuft_total: networkUUFT,
      average_consciousness: Math.floor(avgConsciousness),
      consciousness_distribution: {
        above_threshold: crownNodesList.length,
        below_threshold: crownNodes.size - crownNodesList.length,
        elite_consciousness: crownNodesList.filter(n => n.score >= 5000).length
      },

      // Network Health
      last_reward: coheriumManager.lastRewardTime,
      last_supply_update: coheriumManager.lastSupplyUpdate,
      network_coherence: avgConsciousness >= CONSCIOUSNESS_THRESHOLD ? 'STABLE' : 'UNSTABLE',
      timestamp: Date.now()
    },

    // 🔥 NEW: Crown Node Details
    crown_nodes_detail: crownNodesList.map(node => ({
      id: node.id.substr(0, 8) + '...',
      consciousness_level: node.score,
      crown_rank: node.crownRank,
      dominance_factor: node.consciousnessDominance?.toFixed(2),
      crown_since: node.crownSince,
      estimated_rewards: coheriumManager.calculateUUFTReward(node.score)
    }))
  });
});

// 🔥 NEW: Live consciousness-adjusted token flow demonstration
app.get('/live/token-flows', (req, res) => {
  const crownNodesList = Array.from(crownNodes.values()).filter(n => n.isCrown);
  const flows = [];

  crownNodesList.forEach(node => {
    const baseReward = BLOCK_REWARD / crownNodesList.length;
    const uuftBonus = coheriumManager.calculateUUFTReward(node.score);
    const totalReward = baseReward + uuftBonus;

    flows.push({
      node_id: node.id.substr(0, 8) + '...',
      consciousness_level: node.score,
      base_reward: Math.floor(baseReward),
      uuft_bonus: uuftBonus,
      total_reward: Math.floor(totalReward),
      consciousness_multiplier: (node.score / CONSCIOUSNESS_THRESHOLD).toFixed(2),
      flow_rate: Math.floor(totalReward * 60 / 2) + ' κ/hour', // Assuming 2s blocks
      dominance_factor: node.consciousnessDominance?.toFixed(3)
    });
  });

  res.json({
    success: true,
    timestamp: Date.now(),
    network_consciousness: coheriumManager.getAverageNetworkConsciousness(),
    total_flow_rate: flows.reduce((sum, f) => sum + parseInt(f.flow_rate), 0) + ' κ/hour',
    consciousness_premium: ((coheriumManager.calculateConsciousnessMultiplier() - 1) * 100).toFixed(1) + '%',
    active_flows: flows,
    flow_summary: {
      high_consciousness_nodes: flows.filter(f => f.consciousness_level >= 4000).length,
      elite_consciousness_nodes: flows.filter(f => f.consciousness_level >= 6000).length,
      total_uuft_bonuses: flows.reduce((sum, f) => sum + f.uuft_bonus, 0),
      average_multiplier: flows.length > 0 ?
        (flows.reduce((sum, f) => sum + parseFloat(f.consciousness_multiplier), 0) / flows.length).toFixed(2) : '0'
    }
  });
});

// 🔥 NEW: Real-time consciousness validation test
app.post('/test/consciousness-validation', (req, res) => {
  const { test_uuft_score = 3000 } = req.body;

  const isEligible = test_uuft_score >= CONSCIOUSNESS_THRESHOLD;
  const reward = coheriumManager.calculateUUFTReward(test_uuft_score);
  const multiplier = test_uuft_score / CONSCIOUSNESS_THRESHOLD;

  res.json({
    success: true,
    test_results: {
      input_uuft: test_uuft_score,
      consciousness_threshold: CONSCIOUSNESS_THRESHOLD,
      crown_eligible: isEligible,
      calculated_reward: reward,
      consciousness_multiplier: multiplier.toFixed(3),
      reward_tier: reward === 0 ? 'REJECTED' :
                   reward < 50 ? 'BASIC' :
                   reward < 200 ? 'ENHANCED' : 'ELITE',
      annual_earning_estimate: isEligible ? Math.floor(reward * 365 * 24 * 30) + ' κ/year' : '0 κ/year'
    },
    comparison: {
      vs_minimum: isEligible ? `+${((test_uuft_score - CONSCIOUSNESS_THRESHOLD) / CONSCIOUSNESS_THRESHOLD * 100).toFixed(1)}%` : 'BELOW THRESHOLD',
      vs_network_average: `${test_uuft_score >= coheriumManager.getAverageNetworkConsciousness() ? '+' : ''}${((test_uuft_score - coheriumManager.getAverageNetworkConsciousness()) / coheriumManager.getAverageNetworkConsciousness() * 100).toFixed(1)}%`
    }
  });
});

const PORT = process.env.PORT || 8080;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log('🔗 KetherNet Blockchain running on port', PORT);
  console.log('💎 Coherium validation active');
  console.log('👑 Crown Consensus enabled');
  console.log('💰 Coherium supply:', coheriumManager.totalSupply, '/', MAX_COHERIUM_SUPPLY);
  console.log('🧠 Consciousness threshold:', CONSCIOUSNESS_THRESHOLD, 'UUFT');
  console.log('⚡ Enhanced token economics active');
  console.log('📊 Live endpoints: /live/token-flows, /test/consciousness-validation');
});

module.exports = { app, server, coheriumManager };